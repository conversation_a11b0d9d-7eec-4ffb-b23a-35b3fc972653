[docks]

dock_3_selected_tab_idx=0
dock_4_selected_tab_idx=0
dock_5_selected_tab_idx=0
dock_floating={}
dock_filesystem_h_split_offset=480
dock_filesystem_v_split_offset=0
dock_filesystem_display_mode=0
dock_filesystem_file_sort=0
dock_filesystem_file_list_display_mode=1
dock_filesystem_selected_paths=PackedStringArray("res://scenes/Chapter2.tscn")
dock_filesystem_uncollapsed_paths=PackedStringArray("Favorites", "res://", "res://scenes/", "res://assets/")
dock_node_current_tab=0
dock_history_include_scene=true
dock_history_include_global=true
dock_bottom=[]
dock_closed=[]
dock_split_2=0
dock_split_3=0
dock_hsplit_1=0
dock_hsplit_2=270
dock_hsplit_3=-270
dock_hsplit_4=0
dock_3="Scene,Import"
dock_4="FileSystem"
dock_5="Inspector,Node,History"

[EditorNode]

open_scenes=PackedStringArray("res://scenes/MainMenu.tscn", "res://scenes/ChapterProgressionTest.tscn", "res://scenes/DialogueSystem.tscn", "res://scenes/FontTestScene.tscn", "res://scenes/Chapter2.tscn")
current_scene="res://scenes/Chapter2.tscn"
center_split_offset=-1408
selected_default_debugger_tab_idx=0
selected_main_editor_idx=2
selected_bottom_panel_item=0

[EditorWindow]

screen=1
mode="maximized"
position=Vector2i(3456, 50)
size=Vector2i(3456, 2158)

[ScriptEditor]

open_scripts=["res://scripts/AudioManager.gd", "res://scripts/AudioSettings.gd", "res://scripts/CaesarCipherPuzzle.gd", "res://scripts/Chapter.gd", "res://scripts/ChapterIntro.gd", "res://scripts/DialogueSystem.gd", "res://scripts/font_loader.gd", "res://autoload/GameManager.gd", "res://scripts/MainMenu.gd", "res://scripts/MemoryTestPuzzle.gd", "res://scripts/NavigationPuzzle.gd", "res://scripts/NewChaptersMenu.gd", "res://README.md"]
selected_script="res://scripts/ChapterIntro.gd"
open_help=[]
script_split_offset=400
list_split_offset=0
zoom_factor=1.0

[GameView]

floating_window_rect=Rect2i(0, 0, 0, 0)
floating_window_screen=-1

[ShaderEditor]

open_shaders=[]
split_offset=400
selected_shader=""
text_shader_zoom_factor=1.0

[editor_log]

log_filter_0=true
log_filter_2=true
log_filter_1=true
log_filter_3=true
log_filter_4=true
collapse=false
show_search=true
